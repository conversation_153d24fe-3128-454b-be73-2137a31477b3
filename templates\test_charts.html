<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>Chart Test Page</h1>
        
        <!-- Advanced Growth Analytics Section -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Advanced Growth Analytics</h6>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-light btn-sm chart-toggle-btn active" onclick="switchChart('growth')">Growth</button>
                        <button type="button" class="btn btn-outline-light btn-sm chart-toggle-btn" onclick="switchChart('prediction')">Prediction</button>
                        <button type="button" class="btn btn-outline-light btn-sm chart-toggle-btn" onclick="switchChart('efficiency')">Efficiency</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div style="height: 400px; position: relative;">
                    <canvas id="interactiveGrowthChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentChart = null;

        function switchChart(type) {
            console.log("switchChart called with type:", type);
            
            if (currentChart) {
                currentChart.destroy();
            }

            // Update active button state
            const buttons = document.querySelectorAll('.chart-toggle-btn');
            buttons.forEach(btn => {
                btn.classList.remove('active');
            });

            const activeButton = document.querySelector(`[onclick="switchChart('${type}')"]`);
            if (activeButton) {
                activeButton.classList.add('active');
            }

            const ctx = document.getElementById('interactiveGrowthChart');
            if (!ctx) {
                console.error("Canvas not found!");
                return;
            }

            const growthData = [
                {week: 'Week 1', total_hours: 120, active_tutors: 8, sessions: 45},
                {week: 'Week 2', total_hours: 135, active_tutors: 9, sessions: 52},
                {week: 'Week 3', total_hours: 142, active_tutors: 7, sessions: 48},
                {week: 'Week 4', total_hours: 158, active_tutors: 10, sessions: 61}
            ];

            let config = {};

            switch(type) {
                case 'growth':
                    config = {
                        type: 'line',
                        data: {
                            labels: growthData.map(d => d.week),
                            datasets: [{
                                label: 'Total Hours',
                                data: growthData.map(d => d.total_hours),
                                borderColor: '#007bff',
                                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                                tension: 0.4,
                                fill: true
                            }, {
                                label: 'Active Tutors',
                                data: growthData.map(d => d.active_tutors),
                                borderColor: '#28a745',
                                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                                tension: 0.4,
                                yAxisID: 'y1'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: { beginAtZero: true, title: { display: true, text: 'Hours' } },
                                y1: { type: 'linear', display: true, position: 'right', title: { display: true, text: 'Tutors' }, grid: { drawOnChartArea: false } }
                            }
                        }
                    };
                    break;
                    
                case 'prediction':
                    config = {
                        type: 'bar',
                        data: {
                            labels: ['EWMA', 'Linear Regression', 'Simple Moving Avg'],
                            datasets: [{
                                label: 'Prediction Methods (Hours)',
                                data: [145, 152, 138],
                                backgroundColor: ['#007bff', '#28a745', '#ffc107'],
                                borderColor: ['#0056b3', '#1e7e34', '#e0a800'],
                                borderWidth: 2
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: { display: true, text: 'AI Algorithm Comparison' }
                            }
                        }
                    };
                    break;
                    
                case 'efficiency':
                    config = {
                        type: 'radar',
                        data: {
                            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                            datasets: [{
                                label: 'Hours per Session',
                                data: [2.1, 2.3, 2.0, 2.4, 2.2, 1.8, 1.9],
                                borderColor: '#17a2b8',
                                backgroundColor: 'rgba(23, 162, 184, 0.2)',
                                pointBackgroundColor: '#17a2b8'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: { display: true, text: 'Daily Efficiency Patterns' }
                            }
                        }
                    };
                    break;
            }

            try {
                currentChart = new Chart(ctx, config);
                console.log("Chart created successfully:", type);
            } catch (error) {
                console.error("Error creating chart:", error);
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM loaded, initializing chart...");
            switchChart('growth');
        });
    </script>
</body>
</html>
