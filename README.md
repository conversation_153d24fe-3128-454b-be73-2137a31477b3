# 📋 Tutor Face Recognition Dashboard

This is a Flask-based web application built for Gannon University's STEM Center. It allows staff to manage tutor attendance using face recognition and provides rich analytics through charts and filters.

---

## 🔧 Features

- 📅 Logs grouped by date with collapsible sections
- 🧑 Manual check-in/check-out system with snapshot tracking
- 📊 Interactive analytics dashboard with chart export
- 🌙 Toggle between dark and light mode
- 🔍 Filter logs by Tutor ID and date range
- 📸 Snapshot viewer modal for check-in/out verification
- 🔁 Expand/Collapse all sections at once
- ⬇️ Download attendance logs in CSV format

---

## 🚀 How to Run Locally

```bash
# Clone the repository
git clone https://github.com/AvishManiar21/stem-face-dashboard.git
cd stem-face-dashboard

# Install dependencies
pip install -r requirements.txt

# Run the Flask app
python app.py
